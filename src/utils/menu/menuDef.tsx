import React from "react";
import { Role } from "@prisma/client";

export enum MenuType {
  divider,
  menu,
}

export interface MenuEntry {
  name: string;
  type: MenuType.menu;
  href: string;
  subMenu?: MenuEntry[];
  icon: string;
  role: Role[];
}

export interface MenuDivider {
  name: string;
  type: MenuType.divider;
  role: Role[];
  href?: string;
}

export type MenuItem = MenuEntry | MenuDivider;

export const menuDef: MenuItem[] = [
  {
    name: "Operations",
    type: MenuType.divider,
    role: [Role.USER, Role.ADMIN, Role.CPO],
  },
  {
    name: "Dashboard",
    type: MenuType.menu,
    href: "/",
    icon: "🎛️",
    role: [Role.USER, Role.ADMIN, Role.CARD_MANAGER, Role.CPO],
  },
  {
    name: "Home",
    type: MenuType.menu,
    href: "/",
    icon: "🏠",
    role: [Role.CARD_HOLDER],
  },
  {
    name: "Power Graph",
    type: MenuType.menu,
    href: "/power-visualization",
    icon: "📊",
    role: [Role.ADMIN, Role.CPO],
  },
  {
    name: "Parking",
    type: MenuType.menu,
    href: "/parkingsensor",
    icon: "🅿️",
    role: [Role.ADMIN],
  },
  {
    name: "Financial",
    type: MenuType.divider,
    role: [Role.ADMIN, Role.CPO],
  },
  {
    name: "Forecast",
    href: "/financal-forecast",
    type: MenuType.menu,
    icon: "📈",
    role: [Role.ADMIN, Role.CPO],
  },
  {
    name: "Invoice & Credit",
    href: "/invoice",
    type: MenuType.menu,
    icon: "🧾",
    role: [Role.ADMIN],
  },
  {
    name: "Invoice forecast",
    href: "/invoice-forecast",
    type: MenuType.menu,
    icon: "🕐",
    role: [Role.ADMIN],
  },
  {
    name: "Qonto Transactions",
    href: "/qonto-transactions",
    type: MenuType.menu,
    icon: "🏦",
    role: [Role.ADMIN],
  },
  {
    name: "Stripe",
    href: "/stripe",
    type: MenuType.menu,
    icon: "💳",
    role: [Role.ADMIN],
  },
  {
    name: "CPO Verträge",
    href: "/cpoContract",
    type: MenuType.menu,
    icon: "📄",
    role: [Role.ADMIN],
  },
  {
    name: "Data",
    type: MenuType.divider,
    role: [Role.ADMIN, Role.CPO],
  },
  {
    name: "CDRs",
    href: "/cdr",
    type: MenuType.menu,
    icon: "🗄️",
    role: [Role.ADMIN],
  },
  {
    name: "EMP & CPO",
    href: "/contact",
    type: MenuType.menu,
    icon: "📇",
    role: [Role.ADMIN],
  },

  {
    name: "Flottenkarten (Token)",
    href: "/tokenGroup",
    type: MenuType.menu,
    icon: "🔑",
    role: [Role.ADMIN],
  },
  {
    name: "Tarife",
    href: "/tarif",
    type: MenuType.menu,
    icon: "💰",
    role: [Role.ADMIN],
  },
  {
    name: "Locations",
    type: MenuType.menu,
    href: "/location",
    icon: "🏢",
    role: [Role.ADMIN],
  },
  {
    name: "Maintenance",
    type: MenuType.menu,
    href: "/maintenance",
    icon: "🔧",
    role: [Role.ADMIN, Role.CPO],
  },
  {
    name: "Admin",
    type: MenuType.divider,
    role: [Role.ADMIN],
  },
  {
    name: "Tenant",
    type: MenuType.menu,
    href: "/tenantconfiguration",
    icon: "🏛️",
    role: [Role.ADMIN],
  },
  {
    name: "Benutzerverwaltung",
    type: MenuType.menu,
    href: "/users",
    icon: "👥",
    role: [Role.ADMIN],
  },
  {
    name: "Nutzergruppen",
    type: MenuType.menu,
    href: "/userGroups",
    icon: "👥",
    role: [Role.ADMIN, Role.CARD_MANAGER],
  },
  {
    name: "Command",
    type: MenuType.menu,
    role: [Role.ADMIN],
    icon: "💻",
    href: "/command",
  },
  {
    name: "Log",
    type: MenuType.menu,
    href: "/log",
    icon: "📋",
    role: [Role.ADMIN],
  },
  {
    name: "Ladekarten-Management",
    type: MenuType.divider,
    role: [Role.ADMIN],
  },

  {
    name: "Zahlungsdaten",
    type: MenuType.menu,
    href: "/emp/payment",
    icon: "💰",
    role: [Role.CARD_HOLDER],
  },
  {
    name: "Ladekarten",
    type: MenuType.menu,
    href: "/emp/card",
    icon: "🔌",
    role: [Role.CARD_HOLDER, Role.CARD_MANAGER],
  },
  {
    name: "Mitarbeiter-Ladekarten",
    type: MenuType.menu,
    href: "/emp/card",
    icon: "🔌",
    role: [Role.ADMIN, Role.USER],
  },
  {
    name: "Mein Tarif",
    type: MenuType.menu,
    href: "/emp/tarif/userview",
    icon: "🏷️",
    role: [Role.CARD_HOLDER],
  },
  {
    name: "Firmentarife",
    type: MenuType.menu,
    href: "/emp/tarif/managerview",
    icon: "🏷️",
    role: [Role.CARD_MANAGER],
  },
  {
    name: "Firmentarife Ou",
    type: MenuType.menu,
    href: "/emp/tarif/managerview",
    icon: "🏷️",
    role: [Role.ADMIN],
  },
  {
    name: "Ladevorgänge Ou",
    type: MenuType.menu,
    href: "/emp/charging-history/managerview",
    icon: "📋",
    role: [Role.ADMIN],
  },
  {
    name: "Ladevorgänge",
    type: MenuType.menu,
    href: "/emp/charging-history/userview",
    icon: "📋",
    role: [Role.CARD_HOLDER],
  },
  {
    name: "Ladevorgänge",
    type: MenuType.menu,
    href: "/emp/charging-history/managerview",
    icon: "📋",
    role: [Role.CARD_MANAGER, Role.CPO],
  },

  {
    name: "Rechnungen",
    type: MenuType.menu,
    href: "/emp/invoice/userview",
    icon: "🧾",
    role: [Role.CARD_HOLDER],
  },

  {
    name: "Rechnungen",
    type: MenuType.menu,
    href: "/emp/invoice/managerview",
    icon: "🧾",
    role: [Role.CARD_MANAGER],
  },

  {
    name: "Benutzerverwaltung",
    type: MenuType.menu,
    href: "/users",
    icon: "👥",
    role: [Role.CARD_MANAGER],
  },

  {
    name: "Sonstiges",
    type: MenuType.divider,
    role: [Role.ADMIN, Role.CPO],
  },
  {
    name: "Benachrichtigungen",
    type: MenuType.menu,
    href: "/notifications",
    icon: "🔔",
    role: [Role.ADMIN, Role.CARD_HOLDER, Role.CARD_MANAGER, Role.CPO, Role.USER],
  },

  {
    name: "Profil",
    type: MenuType.menu,
    href: "/profile",
    icon: "👤",
    role: [Role.ADMIN, Role.CARD_HOLDER, Role.CARD_MANAGER, Role.CPO],
  },

  {
    name: "Support",
    type: MenuType.menu,
    href: "/support",
    icon: "❓",
    role: [Role.ADMIN, Role.CARD_HOLDER, Role.CARD_MANAGER, Role.CPO, Role.USER],
  },
];
